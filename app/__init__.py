# This file makes 'app' a Python package
import os
from dotenv import load_dotenv
from flask import Flask
from flask_cors import CORS  # Import per gestire CORS
from datetime import timedelta # Importa timedelta
from flask_jwt_extended import JWTManager # Import per Flask-JWT-Extended
# Importa l'istanza db dal nuovo file extensions
from .extensions import db
from werkzeug.middleware.proxy_fix import ProxyFix

load_dotenv() # Load environment variables from .env

app = Flask(__name__)
# Configura il middleware ProxyFix prima di altre configurazioni
app.wsgi_app = ProxyFix(
    app.wsgi_app, x_for=1, x_proto=1, x_host=1, x_prefix=1
)
# Configura CORS per permettere richieste dal frontend
CORS(app, resources={
    r"/api/*": {
        "origins": ["http://*************:5173", "http://localhost:5173", 
                    "http://localhost:3000", "http://*************:3000",
                    "https://dea3d-backend.prismanet.com", "https://dea3d-cliente.prismanet.com"],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Authorization", "Content-Type"],
        "supports_credentials": True
    }
})
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DB_CONNECTION_STRING')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY') # Aggiungi questa riga per la chiave segreta
app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY') # Configura la secret key per JWT

# Configura la durata dei token JWT dalle variabili d'ambiente
app.config["JWT_ACCESS_TOKEN_EXPIRES"] = timedelta(minutes=int(os.getenv('JWT_ACCESS_TOKEN_EXPIRES_MINUTES', 15)))
app.config["JWT_REFRESH_TOKEN_EXPIRES"] = timedelta(days=int(os.getenv('JWT_REFRESH_TOKEN_EXPIRES_DAYS', 30)))

jwt = JWTManager(app) # Inizializza Flask-JWT-Extended

# Importa modelli e Blueprints qui per evitare importazioni circolari
# I modelli devono essere importati prima di db.init_app(app) se usano l'istanza db
from . import models
from app.api_v1 import init_app as init_api_v1
from app.auth.routes import auth_bp
from app.api_admin.routes import admin_bp
from app.client_auth.routes import client_auth_bp

# Inizializza l'estensione db con l'app DOPO aver importato i modelli
db.init_app(app)

# Inizializza Flask-Admin DOPO db.init_app(app)

# Registra i Blueprints DOPO db.init_app(app)
init_api_v1(app) # Questa riga chiama la funzione init_app nel modulo __init__.py di api_v1
app.register_blueprint(auth_bp)
app.register_blueprint(admin_bp, url_prefix='/api/admin')
app.register_blueprint(client_auth_bp)

# from . import routes # Se hai route generali, importale e registrale qui
