"use client";

import { Card, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Chip } from "@heroui/react";
import { title, subtitle } from "@/components/primitives";
import { useRouter } from "next/navigation";

export default function DashboardPage() {
  const router = useRouter();

  const stats = [
    { label: "PROGETTI DA VALIDARE", value: 5, color: "warning" as const },
    { label: "ORDINI IN PARTENZA", value: 12, color: "success" as const },
    { label: "COMUNICAZIONI", value: 3, color: "primary" as const },
  ];

  const actionButtons = [
    {
      label: "Ordina produzione CAD CAM",
      description: "Gestisci ordini per produzione CAD/CAM",
      href: "/produzione-cad-cam/pagina1",
      icon: "🦷", // Sostituire con icona reale
    },
    {
      label: "Ordina componenti protesici",
      description: "Gestisci ordini per componenti protesici",
      href: "/componenti-protesici",
      icon: "🔧", // Sostituire con icona reale
    },
    {
      label: "Gestione progetti",
      description: "Visualizza e gestisci tutti i progetti",
      href: "/progetti",
      icon: "📋", // Sostituire con icona reale
    },
    {
      label: "Comunicazioni",
      description: "Visualizza messaggi e notifiche",
      href: "/comunicazioni",
      icon: "💬", // Sostituire con icona reale
    },
  ];

  return (
    <div className="flex flex-col gap-6">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <h1 className={title()}>Dashboard</h1>
        <p className={subtitle()}>Benvenuto nel tuo pannello di controllo DEA3D</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {stats.map((stat, index) => (
          <Card key={index} className="border-none bg-gradient-to-br from-default-50 to-default-100 dark:from-default-100 dark:to-default-200">
            <CardBody className="justify-center items-center pb-0">
              <div className="flex flex-col items-center gap-2">
                <Chip color={stat.color} size="lg" className="text-lg">
                  {stat.value}
                </Chip>
                <p className="text-center text-sm font-medium text-default-600">
                  {stat.label}
                </p>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col gap-4">
        <h2 className={title({ size: "sm" })}>Azioni Rapide</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
          {actionButtons.map((action, index) => (
            <Card 
              key={index} 
              isPressable 
              onPress={() => router.push(action.href)}
              className="border-none bg-gradient-to-br from-default-50 to-default-100 dark:from-default-100 dark:to-default-200 hover:shadow-lg transition-shadow"
            >
              <CardBody className="flex flex-row items-center gap-4 p-6">
                <div className="text-4xl">{action.icon}</div>
                <div className="flex flex-col gap-1">
                  <h3 className="text-lg font-semibold">{action.label}</h3>
                  <p className="text-sm text-default-500">{action.description}</p>
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}