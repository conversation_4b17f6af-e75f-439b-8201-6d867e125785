'use client'
import { useState } from 'react';
import { title } from "@/components/primitives";
import { Form, Button, Input, Textarea } from "@heroui/react";
import { CustomSelect } from './components/CustomSelect';
import {
    tecnici,
    opzioniScannerTipo,
    opzioniScannerIntraorale,
    opzioniSoftwareModellazione,
    opzioniScannerLaboratorio,
    opzioniCorriere,
    opzioniFatturazione,
    opzioniIndirizzoSpedizione,
    opzioniPresso,
    opzioniProgettazione,
    type Tecnico
} from './data';

export default function Pagina1() {
    const [formState, setFormState] = useState({
        tecnico: null as string | null,
        telefono: '' as string,
        scannerType: null as string | null,
        scanner: null as string | null,
        software: null as string | null,
        spedizione: null as string | null,
        fatturazione: null as string | null,
        note: '' as string
    });

    const handleChange = (field: keyof typeof formState) => (value: string) => {
        setFormState(prev => ({
            ...prev,
            [field]: value,
            // Reset scanner quando cambia il tipo di scanner
            ...(field === 'scannerType' ? { scanner: null } : {})
        }));
    };

    const handleInputChange = (field: keyof typeof formState) =>
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setFormState(prev => ({
                ...prev,
                [field]: e.target.value
            }));
        };

    const getOptions = (scannerType: string | null) => {
        if (scannerType === "1") return opzioniScannerIntraorale;
        if (scannerType === "2") return opzioniScannerLaboratorio;
        return []; // oppure un array di default o null
    }

    return (
        <div>
            <h1 className={title()}>Informazioni Laboratorio</h1>
            <Form 
                className="space-y-6"
                onSubmit={(e) => {
                    e.preventDefault();
                    // Handle form submission
                    console.log(formState)
                }}
            >
                <div className="flex w-full flex-wrap md:flex-nowrap gap-4">
                    <CustomSelect
                        size="sm"
                        label="Seleziona Tecnico Laboratorio"
                        options={tecnici}
                        isRequired
                        value={formState.tecnico}
                        onChange={handleChange('tecnico')}
                    />
                    <Input
                        size="sm"
                        label="Numero di Cellulare"
                        type="tel"
                        isRequired
                        value={formState.telefono}
                        onChange={handleInputChange('telefono')}
                    />
                </div>

                <h2 className="text-xl font-semibold">Informazioni Scansione</h2>
                <div className="flex w-full flex-wrap md:flex-nowrap gap-4">
                    <CustomSelect
                        label="Tipologia"
                        options={opzioniScannerTipo}
                        defaultSelectedKeys={["1"]}
                        value={formState.scannerType}
                        onChange={handleChange('scannerType')}
                        isRequired={true}
                    />
                    <CustomSelect
                        label="Scanner"
                        options={getOptions(formState.scannerType)}
                        value={formState.scanner}
                        onChange={handleChange('scanner')}
                        isRequired
                    />
                    {formState.scanner === "35" && (
                        <Input
                            label="Scanner Altro"
                        />
                    )}
                    <CustomSelect
                        label="Software di Modellazione"
                        options={opzioniSoftwareModellazione}
                        value={formState.software}
                        onChange={handleChange('software')}
                        defaultSelectedKeys={["3"]}
                        isRequired
                    />
                    {formState.software === '9' &&
                        <Input
                            label="Software Modellazione Altro"
                        />}
                </div>

                <h2 className="text-xl font-semibold">Informazioni Cliente</h2>
                <div className="flex w-full flex-wrap md:flex-nowrap gap-4">
                    <Input
                        label="Codice Paziente (riportato in bolla)"
                    />
                    <Input
                        label="Codice Cliente (riportato in bolla)"
                    />
                </div>

                <h2 className="text-xl font-semibold">Informazioni Spedizione</h2>
                <div className="flex w-full flex-wrap md:flex-nowrap gap-4">
                    <CustomSelect
                        label="Spedizione"
                        options={opzioniIndirizzoSpedizione}
                        value={formState.spedizione}
                        onChange={handleChange('spedizione')}
                        isRequired
                        defaultSelectedKeys={["Principale"]}
                    />
                    {formState.spedizione === 'Altro' &&
                        <Input
                            label="Spedizione Altro"
                        />}
                </div>
                <div className="flex w-full flex-wrap md:flex-nowrap gap-4">
                    <CustomSelect
                        label="Fatturazione"
                        options={opzioniFatturazione}
                        value={formState.fatturazione}
                        onChange={handleChange('fatturazione')}
                        isRequired
                        defaultSelectedKeys={["Principale"]}
                    />
                    {formState.fatturazione === 'Altro' &&
                        <Input
                            label="Fatturazione Altro"
                        />}
                </div>
                <div className="flex w-full flex-wrap md:flex-nowrap gap-4">
                    <CustomSelect
                        label="Corriere"
                        options={opzioniCorriere}
                        isRequired
                        defaultSelectedKeys={["4"]}
                    />
                    <CustomSelect
                        label="Presso"
                        options={opzioniPresso}
                        isRequired
                        defaultSelectedKeys={["Presso Laboratorio"]}
                    />
                    <CustomSelect
                        label="Vuoi la progettazione della struttura da parte di New Ancorvis?"
                        options={opzioniProgettazione}
                        defaultSelectedKeys={["SI"]}
                        isRequired
                    />
                </div>

                <h2 className="text-xl font-semibold">Coupon</h2>
                <div className="flex items-end gap-4">
                    <Input
                        label="Coupon"
                    />
                </div>

                <h2 className="text-xl font-semibold">Note Paziente</h2>
                <Textarea
                    label="Note Paziente"
                    value={formState.note}
                    onChange={handleInputChange('note')}
                />

                <Button type="submit" variant="bordered">Avanti</Button>
            </Form>
        </div>
    );
}