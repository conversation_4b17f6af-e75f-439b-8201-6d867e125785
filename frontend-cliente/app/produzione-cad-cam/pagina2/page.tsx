'use client'

import React, { useState, useEffect } from 'react'
import { TeethMap } from './components/TeethMap'
import { SelectedWorks } from './components/SelectedWorks'
import { TeethWorkModal } from './components/TeethWorkModal'
import { getLavorazioni } from './api'
import type { Lavorazione, ParametroConfigurazione } from './types'
import useLocalStorage from '@/hooks/useLocalStorage'

// Definizione della nuova interfaccia per i valori di teethWorks
interface SelectedWorkEntry {
    lavorazione: Lavorazione;
    parameters: Record<number, number | string | (number | boolean)[]>;
    // Definizioni complete dei parametri dinamici
    dynamicParameterDefinitions: ParametroConfigurazione[];
    // Struttura delle dipendenze tra parametri
    dependencyTree: {
        rootParameterId: number;
        // Mappa dei parametri genitori ai loro figli
        parentToChildMap: Record<number, number[]>;
        // Traccia quali parametri sono stati attivati da quali parametri genitori
        parameterTriggerMap: Record<number, {
            parentId: number;
            parentValueId: number;
        }>;
    } | null;
}

export default function Pagina2() {
    const [selectedTeeth, setSelectedTeeth] = useLocalStorage<string[]>('selectedTeeth', [])
    const [selectedBridges, setSelectedBridges] = useLocalStorage<string[]>('selectedBridges', [])
    // Aggiornamento del tipo di stato per teethWorks
    const [teethWorks, setTeethWorks] = useLocalStorage<Record<string, SelectedWorkEntry>>('teethWorks', {})
    const [modalOpen, setModalOpen] = useState(false)
    const [selectedToothId, setSelectedToothId] = useState<string>('')
    const [lavorazioni, setLavorazioni] = useState<Lavorazione[]>([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState<string | null>(null)

    useEffect(() => {
        const fetchLavorazioni = async () => {
            try {
                const data = await getLavorazioni()
                setLavorazioni(data)
            } catch (err) {
                setError('Errore nel caricamento delle lavorazioni')
                console.error(err)
            } finally {
                setLoading(false)
            }
        }

        fetchLavorazioni()
    }, [])

    const handleTeethSelect = (toothId: string) => {
        setSelectedToothId(toothId)
        setModalOpen(true)
    }

    const handleBridgeSelect = (bridgeId: string) => {
        setSelectedBridges((prev: string[]) =>
            prev.includes(bridgeId)
                ? prev.filter((id: string) => id !== bridgeId)
                : [...prev, bridgeId]
        )
    }

    // Modifica della firma e della logica di handleWorkSave per includere allDynamicParameters e dependencyTree
    const handleWorkSave = (
        lavorazione: Lavorazione | null,
        parameters?: Record<number, number | string | (number | boolean)[]>,
        allDynamicParameters?: ParametroConfigurazione[],
        dependencyTree?: {
            rootParameterId: number;
            parentToChildMap: Record<number, number[]>;
            parameterTriggerMap: Record<number, {
                parentId: number;
                parentValueId: number;
            }>;
        } | null
    ) => {
        setTeethWorks((prev: Record<string, SelectedWorkEntry>) => {
            if (lavorazione === null) {
                // Se la lavorazione è null, significa che è stata rimossa
                const { [selectedToothId]: _, ...rest } = prev;
                return rest;
            }
            return {
                ...prev,
                [selectedToothId]: {
                    lavorazione: lavorazione, // Passa l'oggetto lavorazione completo
                    parameters: parameters || {}, // Assicurati che parameters sia un oggetto
                    dynamicParameterDefinitions: allDynamicParameters || [], // Salva le definizioni dei parametri
                    dependencyTree: dependencyTree || null // Salva la struttura delle dipendenze
                }
            };
        });
        setSelectedTeeth((prev: string[]) => {
            if (lavorazione === null) {
                return prev.filter((id: string) => id !== selectedToothId);
            }
            return prev.includes(selectedToothId) ? prev : [...prev, selectedToothId];
        });
        setModalOpen(false);
    }

    return (
        <div className="p-4">
            {loading ? (
                <div className="text-center py-8">Caricamento lavorazioni...</div>
            ) : error ? (
                <div className="text-red-500 text-center py-8">{error}</div>
            ) : (
                <div className="flex flex-col md:flex-row gap-6">
                    <div className="w-full">
                        <TeethMap
                            onTeethSelect={handleTeethSelect}
                            onBridgeSelect={handleBridgeSelect}
                            selectedTeeth={selectedTeeth}
                            selectedBridges={selectedBridges}
                            teethWorks={teethWorks}
                        />
                    </div>
                    <div className="w-full">
                        {/* Passa le definizioni dei parametri a SelectedWorks */}
                        <SelectedWorks selectedTeeth={selectedTeeth} teethWorks={teethWorks} />
                    </div>
                </div>
            )}
            <TeethWorkModal
                isOpen={modalOpen}
                onClose={() => setModalOpen(false)}
                onSave={handleWorkSave}
                toothId={selectedToothId}
                // Aggiornamento di currentWorkType per passare l'id_lavorazione
                currentWorkType={teethWorks[selectedToothId]?.lavorazione?.id_lavorazione?.toString()}
                currentParameters={teethWorks[selectedToothId]?.parameters}
                currentDynamicParameters={teethWorks[selectedToothId]?.dynamicParameterDefinitions}
                currentDependencyTree={teethWorks[selectedToothId]?.dependencyTree}
                availableWorkTypes={lavorazioni}
            />
        </div>
    )
}