"use client";

import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { Button } from "@heroui/button";
import { Input } from "@heroui/input";
import { Avatar } from "@heroui/avatar";
import { title, subtitle } from "@/components/primitives";
import { UserIcon } from "@/components/icons";

export default function ProfilePage() {
  return (
    <div className="flex flex-col gap-6 max-w-4xl">
      {/* Header */}
      <div className="flex flex-col gap-2">
        <h1 className={title()}>Profilo Utente</h1>
        <p className={subtitle()}>Gestisci le tue informazioni personali</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Card Profilo */}
        <Card className="lg:col-span-1">
          <CardHeader className="flex flex-col items-center gap-4 pb-0">
            <Avatar
              size="lg"
              icon={<UserIcon size={40} />}
              className="w-24 h-24"
            />
            <div className="text-center">
              <h3 className="text-lg font-semibold">Mario Rossi</h3>
              <p className="text-sm text-default-500">Odontotecnico</p>
            </div>
          </CardHeader>
          <CardBody className="pt-4">
            <div className="flex flex-col gap-2 text-sm">
              <div>
                <span className="font-medium">Email:</span>
                <p className="text-default-600"><EMAIL></p>
              </div>
              <div>
                <span className="font-medium">Telefono:</span>
                <p className="text-default-600">+39 ************</p>
              </div>
              <div>
                <span className="font-medium">Ruolo:</span>
                <p className="text-default-600">Amministratore</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Form Modifica */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <h3 className="text-lg font-semibold">Modifica Informazioni</h3>
          </CardHeader>
          <CardBody>
            <form className="flex flex-col gap-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  label="Nome"
                  placeholder="Inserisci il nome"
                  defaultValue="Mario"
                />
                <Input
                  label="Cognome"
                  placeholder="Inserisci il cognome"
                  defaultValue="Rossi"
                />
              </div>
              
              <Input
                type="email"
                label="Email"
                placeholder="Inserisci l'email"
                defaultValue="<EMAIL>"
              />
              
              <Input
                type="tel"
                label="Telefono"
                placeholder="Inserisci il telefono"
                defaultValue="+39 ************"
              />
              
              <Input
                label="Azienda"
                placeholder="Inserisci l'azienda"
                defaultValue="DEA3D Lab"
              />

              <div className="flex gap-3 justify-end mt-4">
                <Button variant="flat">
                  Annulla
                </Button>
                <Button color="primary">
                  Salva Modifiche
                </Button>
              </div>
            </form>
          </CardBody>
        </Card>
      </div>

      {/* Sezione Sicurezza */}
      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">Sicurezza</h3>
        </CardHeader>
        <CardBody>
          <div className="flex flex-col gap-4">
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-medium">Cambia Password</h4>
                <p className="text-sm text-default-500">
                  Aggiorna la tua password per mantenere sicuro l'account
                </p>
              </div>
              <Button variant="flat" color="primary">
                Cambia Password
              </Button>
            </div>
            
            <div className="flex justify-between items-center">
              <div>
                <h4 className="font-medium">Autenticazione a Due Fattori</h4>
                <p className="text-sm text-default-500">
                  Aggiungi un livello extra di sicurezza al tuo account
                </p>
              </div>
              <Button variant="flat" color="secondary">
                Configura 2FA
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  );
}