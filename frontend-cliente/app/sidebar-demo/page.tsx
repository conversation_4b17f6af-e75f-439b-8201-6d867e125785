"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@heroui/button";
import { Card, CardBody, CardHeader } from "@heroui/card";
import { Divider } from "@heroui/divider";

import { Sidebar } from "@/components/sidebar";
import { SidebarItem } from "@/types";
import {
  HomeIcon,
  DashboardIcon,
  SettingsIcon,
  UsersIcon,
  DocumentIcon,
  Logo,
} from "@/components/icons";

export default function SidebarDemoPage() {
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Esempio di array di elementi per la sidebar
  const sidebarItems: SidebarItem[] = [
    {
      label: "Home",
      href: "/",
      icon: <HomeIcon size={20} />,
    },
    {
      label: "Dashboard",
      href: "/dashboard",
      icon: <DashboardIcon size={20} />,
    },
    {
      label: "Utenti",
      href: "/users",
      icon: <UsersIcon size={20} />,
    },
    {
      label: "Documenti",
      href: "/documents",
      icon: <DocumentIcon size={20} />,
    },
    {
      label: "Impostazioni",
      href: "/settings",
      icon: <SettingsIcon size={20} />,
    },
    {
      label: "Elemento Disabilitato",
      href: "/disabled",
      icon: <DocumentIcon size={20} />,
      isDisabled: true,
    },
  ];

  const sidebarHeader = (
    <div className="flex items-center gap-2">
      <Logo size={24} />
      <span className="font-bold text-lg">DEA3D</span>
    </div>
  );

  const sidebarFooter = (
    <div className="text-xs text-default-500 text-center">
      © 2024 DEA3D
    </div>
  );

  return (
    <div className="flex h-screen bg-default-50">
      {/* Sidebar */}
      <div className="flex-shrink-0">
        <Sidebar
          items={sidebarItems}
          isCollapsed={isCollapsed}
          onToggle={() => setIsCollapsed(!isCollapsed)}
          header={sidebarHeader}
          footer={sidebarFooter}
          className="h-full"
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 p-6 overflow-auto">
        <Card className="max-w-4xl mx-auto">
          <CardHeader>
            <h1 className="text-2xl font-bold">Demo Sidebar Component</h1>
          </CardHeader>
          <Divider />
          <CardBody className="space-y-6">
            <div>
              <h2 className="text-xl font-semibold mb-3">Caratteristiche del Componente Sidebar</h2>
              <ul className="space-y-2 text-default-600">
                <li>• <strong>Responsive:</strong> Si adatta automaticamente alle dimensioni dello schermo</li>
                <li>• <strong>Collassabile:</strong> Può essere compressa per risparmiare spazio</li>
                <li>• <strong>Tooltip:</strong> Mostra tooltip quando è compressa</li>
                <li>• <strong>Stato attivo:</strong> Evidenzia automaticamente la pagina corrente</li>
                <li>• <strong>Icone personalizzabili:</strong> Supporta icone React personalizzate</li>
                <li>• <strong>Header e Footer:</strong> Supporta contenuto personalizzato in header e footer</li>
                <li>• <strong>Elementi disabilitati:</strong> Supporta elementi non cliccabili</li>
              </ul>
            </div>

            <Divider />

            <div>
              <h2 className="text-xl font-semibold mb-3">Controlli Demo</h2>
              <div className="flex gap-4">
                <Button
                  color="primary"
                  variant="flat"
                  onPress={() => setIsCollapsed(!isCollapsed)}
                >
                  {isCollapsed ? "Espandi" : "Comprimi"} Sidebar
                </Button>
              </div>
            </div>

            <Divider />

            <div>
              <h2 className="text-xl font-semibold mb-3">Esempio di Utilizzo</h2>
              <pre className="bg-default-100 p-4 rounded-lg overflow-x-auto text-sm">
{`import { Sidebar } from "@/components/sidebar";
import { SidebarItem } from "@/types";
import { HomeIcon, DashboardIcon } from "@/components/icons";

const sidebarItems: SidebarItem[] = [
  {
    label: "Home",
    href: "/",
    icon: <HomeIcon size={20} />,
  },
  {
    label: "Dashboard", 
    href: "/dashboard",
    icon: <DashboardIcon size={20} />,
  },
];

<Sidebar
  items={sidebarItems}
  isCollapsed={isCollapsed}
  onToggle={() => setIsCollapsed(!isCollapsed)}
  header={<div>My App</div>}
  footer={<div>© 2024</div>}
/>`}
              </pre>
            </div>

            <Divider />

            <div>
              <h2 className="text-xl font-semibold mb-3">Props Disponibili</h2>
              <div className="space-y-3">
                <div>
                  <code className="bg-default-100 px-2 py-1 rounded text-sm font-mono">items: SidebarItem[]</code>
                  <p className="text-sm text-default-600 mt-1">Array di elementi da visualizzare nella sidebar</p>
                </div>
                <div>
                  <code className="bg-default-100 px-2 py-1 rounded text-sm font-mono">isCollapsed?: boolean</code>
                  <p className="text-sm text-default-600 mt-1">Stato di compressione della sidebar (controllato)</p>
                </div>
                <div>
                  <code className="bg-default-100 px-2 py-1 rounded text-sm font-mono">onToggle?: () = void</code>
                  <p className="text-sm text-default-600 mt-1">Callback chiamata quando si clicca il pulsante di toggle</p>
                </div>
                <div>
                  <code className="bg-default-100 px-2 py-1 rounded text-sm font-mono">header?: React.ReactNode</code>
                  <p className="text-sm text-default-600 mt-1">Contenuto personalizzato per l'header</p>
                </div>
                <div>
                  <code className="bg-default-100 px-2 py-1 rounded text-sm font-mono">footer?: React.ReactNode</code>
                  <p className="text-sm text-default-600 mt-1">Contenuto personalizzato per il footer</p>
                </div>
                <div>
                  <code className="bg-default-100 px-2 py-1 rounded text-sm font-mono">className?: string</code>
                  <p className="text-sm text-default-600 mt-1">Classi CSS aggiuntive</p>
                </div>
              </div>
            </div>

            <Divider />

            <div>
              <h2 className="text-xl font-semibold mb-3">Interfaccia SidebarItem</h2>
              <pre className="bg-default-100 p-4 rounded-lg overflow-x-auto text-sm">
{`interface SidebarItem {
  label: string;           // Testo da visualizzare
  href: string;            // URL di destinazione
  icon?: React.ReactNode;  // Icona opzionale
  isActive?: boolean;      // Stato attivo manuale (opzionale)
  isDisabled?: boolean;    // Elemento disabilitato
}`}
              </pre>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
}