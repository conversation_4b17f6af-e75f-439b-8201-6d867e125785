"use client";

import { usePathname } from "next/navigation";
import {
  Navbar as Hero<PERSON><PERSON>avbar,
  NavbarContent,
  NavbarBrand,
  NavbarItem,
} from "@heroui/navbar";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import { Tooltip } from "@heroui/tooltip";
import NextLink from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

import {
  PhoneIcon,
  LogoutIcon,
  UserIcon,
} from "@/components/icons";

export default function ConditionalNavbar() {
  const pathname = usePathname();
  const router = useRouter();
  
  // Pagine che NON sono autenticate (homepage e login)
  const unauthenticatedPages = ['/', '/login'];
  const isAuthenticated = !unauthenticatedPages.includes(pathname);

  const handleLogout = () => {
    // In futuro qui implementeremo la logica di logout reale
    router.push("/");
  };

  const handleProfile = () => {
    // In futuro qui implementeremo la navigazione al profilo
    router.push("/profile");
  };

  const handleLogin = () => {
    router.push("/login");
  };

  return (
    <HeroUINavbar 
      maxWidth="full" 
      position="sticky"
      className="shadow-sm border-b border-default-200 bg-black dark:bg-black"
    >
      {/* Sezione Sinistra - Logo */}
      <NavbarContent justify="start">
        <NavbarBrand>
          <NextLink href={isAuthenticated ? "/dashboard" : "/"} className="flex items-center">
            <Image
              src="/images/logo_200.png"
              alt="DEA3D Logo"
              width={120}
              height={40}
              className="h-10 w-auto"
              onError={(e) => {
                // Fallback se l'immagine non esiste
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
            {/* Fallback text logo se l'immagine non esiste */}
            <div className="hidden">
              <span className="font-bold text-xl text-primary">DEA3D</span>
            </div>
          </NextLink>
        </NavbarBrand>
      </NavbarContent>

      {/* Sezione Centro - Telefono */}
      <NavbarContent justify="center">
        <NavbarItem>
          <Link
            href="tel:+390123456789"
            className="flex items-center gap-2 text-default-700 hover:text-primary transition-colors"
          >
            <PhoneIcon size={20} />
            <span className="font-medium hidden sm:inline">+39 ************</span>
            <span className="font-medium sm:hidden">************</span>
          </Link>
        </NavbarItem>
      </NavbarContent>

      {/* Sezione Destra - Condizionale */}
      <NavbarContent justify="end">
        <NavbarItem>
          {isAuthenticated ? (
            // Navbar per utenti autenticati
            <div className="flex items-center gap-2">
              {/* Bottone Profilo */}
              <Tooltip content="Profilo Utente">
                <Button
                  isIconOnly
                  variant="light"
                  onPress={handleProfile}
                  className="text-default-600 hover:text-primary"
                >
                  <UserIcon size={20} />
                </Button>
              </Tooltip>

              {/* Bottone Logout */}
              <Tooltip content="Logout">
                <Button
                  isIconOnly
                  variant="light"
                  onPress={handleLogout}
                  className="text-default-600 hover:text-danger"
                >
                  <LogoutIcon size={20} />
                </Button>
              </Tooltip>
            </div>
          ) : (
            // Navbar per utenti non autenticati
            <Button
              color="primary"
              variant="flat"
              onPress={handleLogin}
              className="font-medium"
            >
              Accedi
            </Button>
          )}
        </NavbarItem>
      </NavbarContent>
    </HeroUINavbar>
  );
}