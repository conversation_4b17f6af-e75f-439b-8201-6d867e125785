"use client";

import {
  Navbar as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Navbar<PERSON><PERSON>nt,
  Navbar<PERSON>rand,
  Navbar<PERSON>tem,
} from "@heroui/navbar";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import { Tooltip } from "@heroui/tooltip";
import NextLink from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";

import {
  PhoneIcon,
  LogoutIcon,
  UserIcon,
} from "@/components/icons";

export const Navbar = () => {
  const router = useRouter();

  const handleLogout = () => {
    // In futuro qui implementeremo la logica di logout reale
    router.push("/");
  };

  const handleProfile = () => {
    // In futuro qui implementeremo la navigazione al profilo
    router.push("/profile");
  };

  return (
    <HeroUINavbar 
      maxWidth="full" 
      position="sticky"
      className="shadow-sm border-b border-default-200 bg-black dark:bg-black"
    >
      {/* Sezione Sinistra - Logo */}
      <NavbarContent justify="start">
        <NavbarBrand>
          <NextLink href="/" className="flex items-center">
            <Image
              src="/images/logo_200.png"
              alt="DEA3D Logo"
              width={120}
              height={40}
              className="h-10 w-auto"
              onError={(e) => {
                // Fallback se l'immagine non esiste
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
            {/* Fallback text logo se l'immagine non esiste */}
            <div className="hidden">
              <span className="font-bold text-xl text-primary">DEA3D</span>
            </div>
          </NextLink>
        </NavbarBrand>
      </NavbarContent>

      {/* Sezione Centro - Telefono */}
      <NavbarContent justify="center">
        <NavbarItem>
          <Link
            href="tel:+390123456789"
            className="flex items-center gap-2 text-default-700 hover:text-primary transition-colors"
          >
            <PhoneIcon size={20} />
            <span className="font-medium">+39 ************</span>
          </Link>
        </NavbarItem>
      </NavbarContent>

      {/* Sezione Destra - Profilo e Logout */}
      <NavbarContent justify="end">
        <NavbarItem>
          <div className="flex items-center gap-2">
            {/* Bottone Profilo */}
            <Tooltip content="Profilo Utente">
              <Button
                isIconOnly
                variant="light"
                onPress={handleProfile}
                className="text-default-600 hover:text-primary"
              >
                <UserIcon size={20} />
              </Button>
            </Tooltip>

            {/* Bottone Logout */}
            <Tooltip content="Logout">
              <Button
                isIconOnly
                variant="light"
                onPress={handleLogout}
                className="text-default-600 hover:text-danger"
              >
                <LogoutIcon size={20} />
              </Button>
            </Tooltip>
          </div>
        </NavbarItem>
      </NavbarContent>
    </HeroUINavbar>
  );
};