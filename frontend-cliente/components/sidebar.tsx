"use client";

import { useState } from "react";
import { Card, CardBody } from "@heroui/card";
import { Button } from "@heroui/button";
import { Link } from "@heroui/link";
import { Divider } from "@heroui/divider";
import { Tooltip } from "@heroui/tooltip";
import NextLink from "next/link";
import { usePathname } from "next/navigation";
import clsx from "clsx";

import { SidebarProps, SidebarItem } from "@/types";

export const Sidebar = ({
  items,
  className,
  isCollapsed: controlledCollapsed,
  onToggle,
  header,
  footer,
}: SidebarProps) => {
  const [internalCollapsed, setInternalCollapsed] = useState(false);
  const pathname = usePathname();

  // Use controlled state if provided, otherwise use internal state
  const isCollapsed = controlledCollapsed !== undefined ? controlledCollapsed : internalCollapsed;
  
  const handleToggle = () => {
    if (onToggle) {
      onToggle();
    } else {
      setInternalCollapsed(!internalCollapsed);
    }
  };

  const renderSidebarItem = (item: SidebarItem, index: number) => {
    const isActive = item.isActive !== undefined ? item.isActive : pathname === item.href;
    
    const linkContent = (
      <div className={clsx(
        "flex items-center gap-3 p-3 rounded-lg transition-all duration-200",
        "hover:bg-default-100 active:bg-default-200",
        {
          "bg-primary-50 text-primary-600 border-l-4 border-primary": isActive,
          "text-default-600": !isActive,
          "opacity-50 cursor-not-allowed": item.isDisabled,
          "justify-center": isCollapsed,
          "justify-start": !isCollapsed,
        }
      )}
    >
      {item.icon && (
        <div className={clsx(
          "flex-shrink-0",
          {
            "text-primary": isActive,
            "text-default-400": !isActive,
          }
        )}>
          {item.icon}
        </div>
      )}
      {!isCollapsed && (
        <span className={clsx(
          "font-medium text-sm transition-opacity duration-200",
          {
            "text-primary-600": isActive,
            "text-default-600": !isActive,
          }
        )}>
          {item.label}
        </span>
      )}
    </div>
    );

    if (item.isDisabled) {
      return (
        <div key={index} className="cursor-not-allowed">
          {linkContent}
        </div>
      );
    }

    const linkElement = (
      <NextLink key={index} href={item.href} className="block">
        {linkContent}
      </NextLink>
    );

    // If collapsed, wrap in tooltip
    if (isCollapsed) {
      return (
        <Tooltip key={index} content={item.label} placement="right">
          {linkElement}
        </Tooltip>
      );
    }

    return linkElement;
  };

  return (
    <Card 
      className={clsx(
        "h-full transition-all duration-300 ease-in-out",
        {
          "w-16": isCollapsed,
          "w-64": !isCollapsed,
        },
        className
      )}
      shadow="sm"
    >
      <CardBody className="p-0 flex flex-col h-full">
        {/* Header */}
        {header && (
          <>
            <div className={clsx(
              "p-4 flex items-center",
              {
                "justify-center": isCollapsed,
                "justify-between": !isCollapsed,
              }
            )}>
              {!isCollapsed ? header : null}
              <Button
                isIconOnly
                size="sm"
                variant="light"
                onPress={handleToggle}
                className="ml-auto"
              >
                <svg
                  className={clsx(
                    "w-4 h-4 transition-transform duration-200",
                    {
                      "rotate-180": isCollapsed,
                    }
                  )}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M11 19l-7-7 7-7m8 14l-7-7 7-7"
                  />
                </svg>
              </Button>
            </div>
            <Divider />
          </>
        )}

        {/* Navigation Items */}
        <nav className="flex-1 p-2 space-y-1">
          {items.map((item, index) => renderSidebarItem(item, index))}
        </nav>

        {/* Footer */}
        {footer && (
          <>
            <Divider />
            <div className={clsx(
              "p-4",
              {
                "flex justify-center": isCollapsed,
              }
            )}>
              {!isCollapsed ? footer : null}
            </div>
          </>
        )}
      </CardBody>
    </Card>
  );
};

export default Sidebar;