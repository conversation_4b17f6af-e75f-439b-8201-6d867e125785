"use client";

import { createContext, useContext, useState, useEffect, ReactNode } from "react";

interface AuthContextType {
  isAuthenticated: boolean;
  login: () => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Verifica se l'utente è autenticato al caricamento
  useEffect(() => {
    // Per ora controlliamo se siamo in una pagina che richiede autenticazione
    const currentPath = window.location.pathname;
    const protectedPaths = ['/dashboard', '/produzione-cad-cam', '/progetti', '/comunicazioni'];
    
    if (protectedPaths.some(path => currentPath.startsWith(path))) {
      // In futuro qui controlleremo un token JWT o simile
      setIsAuthenticated(true);
    }
  }, []);

  const login = () => {
    setIsAuthenticated(true);
    // In futuro qui salveremo il token
  };

  const logout = () => {
    setIsAuthenticated(false);
    // In futuro qui rimuoveremo il token
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}