class Cliente(db.Model):
    __tablename__ = 'clienti'
    
    id_cliente = db.Column(db.Integer, primary_key=True, autoincrement=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    hashed_password = db.Column(db.String(255), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    
    # Dati anagrafici
    nome = db.Column(db.String(100), nullable=False)
    cognome = db.Column(db.String(100), nullable=False)
    ragione_sociale = db.Column(db.String(255), nullable=True)  # Per aziende
    partita_iva = db.Column(db.String(20), nullable=True)
    codice_fiscale = db.Column(db.String(16), nullable=True)
    
    # Contatti
    telefono = db.Column(db.String(20), nullable=True)
    cellulare = db.Column(db.String(20), nullable=True)
    indirizzo = db.Column(db.String(255), nullable=True)
    citta = db.Column(db.String(100), nullable=True)
    cap = db.Column(db.String(10), nullable=True)
    provincia = db.Column(db.String(5), nullable=True)
    nazione = db.Column(db.String(100), nullable=False, default='Italia')
    
    # Dati bancari e pagamento
    iban = db.Column(db.String(34), nullable=True)  # IBAN standard europeo
    modalita_pagamento = db.Column(db.String(100), nullable=True)  # es: "Bonifico", "RID", "Contanti", etc.
    
    # Fatturazione elettronica
    codice_sdi = db.Column(db.String(7), nullable=True)  # Codice destinatario SDI (7 caratteri)
    indirizzo_pec = db.Column(db.String(255), nullable=True)  # PEC per fatturazione elettronica
    
    # Note
    note = db.Column(db.TEXT, nullable=True)
    
    # Stato e metadati
    attivo = db.Column(db.Boolean, default=True, nullable=False)
    data_creazione = db.Column(db.DateTime, default=datetime.datetime.now)
    data_ultima_modifica = db.Column(db.DateTime, default=datetime.datetime.now, onupdate=datetime.datetime.now)
    ultimo_accesso = db.Column(db.DateTime, nullable=True)
    
    def set_password(self, password):
        self.hashed_password = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.hashed_password, password)
    
    @property
    def nome_completo(self):
        return f"{self.nome} {self.cognome}"
    
    @property
    def denominazione(self):
        """Ritorna ragione sociale se presente, altrimenti nome completo"""
        return self.ragione_sociale if self.ragione_sociale else self.nome_completo

    def __repr__(self):
        return f'<Cliente {self.username} - {self.denominazione}>'